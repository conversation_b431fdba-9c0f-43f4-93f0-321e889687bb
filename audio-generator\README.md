# 🎤 Sistema de Geração de Áudio - V4.0 Estratégia Híbrida

Sistema otimizado para geração automática de áudio usando ElevenLabs API com estratégia híbrida anti-bloqueio.

## 🚀 NOVA ESTRATÉGIA HÍBRIDA

### ✨ Funcionalidades Principais:
- **Rotação Rápida**: Máximo 2 chamadas por chave API
- **Blocos Maiores**: Até 2500 caracteres por chamada
- **Troca de IP Inteligente**: Apenas quando chave é bloqueada (erro 401)
- **Anti-Bloqueio**: Evita detecção de uso abusivo
- **Retomada Automática**: Continua de onde parou

## 📁 Estrutura do Projeto

```
audio-generator/
├── scripts/                          # Scripts principais
│   ├── api_key_manager_sequential.py # Gerenciador de chaves API
│   ├── processador_sequencial.py     # Processador de arquivos
│   ├── arquivo_3_script_principal.py # Script principal
│   ├── utils_sequencial.py           # Utilitários
│   ├── narrador_sequencial_main.py   # Main alternativo
│   ├── elevenlabs_api_atualizado.py  # API ElevenLabs
│   └── config_manager.py             # Gerenciador de configuração
├── config/                           # Configurações
│   ├── config_narracao.json          # Configuração principal
│   ├── chaves-api-elevenlabs.txt     # Chaves API ativas
│   ├── chaves-usadas.txt             # Chaves esgotadas
│   └── processing_state.json         # Estado do processamento
├── roteiros_gerados/                 # Arquivos de entrada (roteiros)
├── narracao_sequencial/              # Arquivos de saída (áudios)
├── logs/                             # Logs do sistema
├── run_audio_generator.py            # Script principal simplificado
└── README.md                         # Este arquivo
```

## 🚀 Como Usar

### Execução Rápida
```bash
python run_audio_generator.py
```

### Execução Avançada
```bash
python scripts/arquivo_3_script_principal.py
```

## ⚙️ Configuração

1. **Chaves API**: Adicione suas chaves ElevenLabs em `config/chaves-api-elevenlabs.txt`
2. **Configuração**: Ajuste parâmetros em `config/config_narracao.json`
3. **Roteiros**: Coloque arquivos de texto em `roteiros_gerados/`

## 📋 Funcionalidades

- ✅ **Rotação Inteligente**: Máximo 2 chamadas por chave
- ✅ **Blocos Otimizados**: Até 2500 caracteres por chamada
- ✅ **Troca de IP Automática**: Apenas quando necessário (erro 401)
- ✅ **Processamento Sequencial**: Retoma de onde parou
- ✅ **Gerenciamento de 127+ chaves API**
- ✅ **Logs detalhados** com progresso em tempo real
- ✅ **Configuração flexível** via JSON

## 🔄 Como Funciona a Estratégia Híbrida

1. **Rotação Normal**: Sistema usa cada chave por no máximo 2 chamadas
2. **Detecção de Bloqueio**: Se receber erro 401, identifica chave bloqueada
3. **Troca de IP**: Para e solicita troca manual de IP
4. **Continuação**: Após troca de IP, retoma com nova chave
5. **Prevenção**: Evita uso abusivo distribuindo chamadas

## 🔧 Dependências

- Python 3.7+
- requests
- pathlib
- json

## 📝 Logs

Os logs são salvos em `logs/` e contêm informações detalhadas sobre o processamento.
